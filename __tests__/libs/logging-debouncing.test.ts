/**
 * Comprehensive tests for logging debouncing functionality
 * Tests debouncing behavior, edge cases, memory management, and integration
 */

import {
  logEventWithContext,
  getDebounceStats,
  cleanupDebouncer,
  clearStoredEvents,
} from '@/app/libs/logging'
import {
  DEBOUNCE_CONFIG,
  EVENT_DEBOUNCE_INTERVALS,
} from '@/app/configs/logging-config'

// Mock PostHog to avoid external dependencies
jest.mock('@/app/libs/posthog', () => ({
  trackEvent: jest.fn(),
  identifyUser: jest.fn(),
}))

// Mock console methods to avoid noise in tests
const originalConsoleLog = console.log
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

beforeAll(() => {
  console.log = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()
})

afterAll(() => {
  console.log = originalConsoleLog
  console.warn = originalConsoleWarn
  console.error = originalConsoleError
})

describe('Logging Debouncing System', () => {
  beforeEach(() => {
    // Clean up before each test
    cleanupDebouncer()
    clearStoredEvents()
    jest.clearAllMocks()
  })

  afterEach(() => {
    // Clean up after each test
    cleanupDebouncer()
  })

  describe('Basic Debouncing Behavior', () => {
    it('should debounce rapid successive calls to the same event', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent

      // Trigger the same event multiple times rapidly
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Should not have been called yet (debounced)
      expect(mockTrackEvent).not.toHaveBeenCalled()

      // Check debounce stats
      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1)
      expect(stats.enabled).toBe(true)
    })

    it('should allow different events to log simultaneously', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent

      // Trigger different events
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      logEventWithContext('click_upgrade_hint', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        message: 'Test hint',
      })

      // Both should be debounced separately
      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(2)
    })

    it('should respect custom debounce intervals for specific events', () => {
      // Test that events with custom intervals are configured correctly
      expect(EVENT_DEBOUNCE_INTERVALS.click_reactflow_hierarchical).toBe(1000)
      expect(EVENT_DEBOUNCE_INTERVALS.submit_feedback_widget).toBe(5000)
      expect(EVENT_DEBOUNCE_INTERVALS.start_subscription_checkout).toBe(10000)
    })
  })

  describe('Event Execution After Debounce', () => {
    it('should execute the event after debounce interval', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent

      // Use a shorter interval for testing
      const originalInterval = DEBOUNCE_CONFIG.defaultIntervalMs

      // Trigger event
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Should not be called immediately
      expect(mockTrackEvent).not.toHaveBeenCalled()

      // Wait for debounce interval + buffer
      await new Promise(resolve => setTimeout(resolve, originalInterval + 100))

      // Should have been called after debounce
      expect(mockTrackEvent).toHaveBeenCalledTimes(1)
      expect(mockTrackEvent).toHaveBeenCalledWith(
        'click_upgrade_button',
        expect.objectContaining({
          context: 'test',
          current_tier: 'FREE',
          button_text: 'Upgrade',
        })
      )
    })

    it('should only execute the latest event when multiple rapid calls occur', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent

      // Trigger multiple events with different properties
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test1',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      await new Promise(resolve => setTimeout(resolve, 100))
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test2',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      await new Promise(resolve => setTimeout(resolve, 100))
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test3',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Wait for debounce interval
      await new Promise(resolve =>
        setTimeout(resolve, DEBOUNCE_CONFIG.defaultIntervalMs + 100)
      )

      // Should only have been called once with the latest properties
      expect(mockTrackEvent).toHaveBeenCalledTimes(1)
      expect(mockTrackEvent).toHaveBeenCalledWith(
        'click_upgrade_button',
        expect.objectContaining({
          context: 'test3',
        })
      )
    })
  })

  describe('Memory Management', () => {
    it('should clean up debounce entries after execution', async () => {
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Should have active entry
      let stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1)

      // Wait for execution
      await new Promise(resolve =>
        setTimeout(resolve, DEBOUNCE_CONFIG.defaultIntervalMs + 100)
      )

      // Should be cleaned up
      stats = getDebounceStats()
      expect(stats.activeEntries).toBe(0)
    })

    it('should handle cleanup when debouncer is manually cleaned', () => {
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      logEventWithContext('click_upgrade_hint', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        message: 'Test hint',
      })

      // Should have active entries
      let stats = getDebounceStats()
      expect(stats.activeEntries).toBe(2)

      // Manual cleanup
      cleanupDebouncer()

      // Should be cleaned up
      stats = getDebounceStats()
      expect(stats.activeEntries).toBe(0)
    })
  })

  describe('Type-Safe Integration', () => {
    it('should work with logEventWithContext function', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent

      // Test with type-safe function
      logEventWithContext('click_aipane_startGenerate', 'user123', 'tree456', {
        prompt_length: 100,
        context_count: 5,
        total_tokens: 1000,
        settings_type: 'generate',
      })

      // Should be debounced
      expect(mockTrackEvent).not.toHaveBeenCalled()

      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1)
    })
  })

  describe('Configuration Validation', () => {
    it('should have valid debounce configuration', () => {
      expect(DEBOUNCE_CONFIG.enabled).toBe(true)
      expect(DEBOUNCE_CONFIG.defaultIntervalMs).toBeGreaterThan(0)
      expect(DEBOUNCE_CONFIG.maxDebounceEntries).toBeGreaterThan(0)
      expect(DEBOUNCE_CONFIG.cleanupIntervalMs).toBeGreaterThan(0)
    })

    it('should have reasonable debounce intervals for events', () => {
      // Check that all configured intervals are reasonable
      Object.entries(EVENT_DEBOUNCE_INTERVALS).forEach(
        ([eventName, interval]) => {
          expect(interval).toBeGreaterThan(0)
          expect(interval).toBeLessThanOrEqual(30000) // Max 30 seconds
        }
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle errors in debounced logging gracefully', async () => {
      const mockTrackEvent = require('@/app/libs/posthog').trackEvent
      mockTrackEvent.mockImplementationOnce(() => {
        throw new Error('PostHog error')
      })

      // Trigger event
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Wait for execution
      await new Promise(resolve =>
        setTimeout(resolve, DEBOUNCE_CONFIG.defaultIntervalMs + 100)
      )

      // Should have attempted to call and handled error gracefully
      expect(mockTrackEvent).toHaveBeenCalledTimes(1)

      // Debounce entry should be cleaned up even after error
      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(0)
    })
  })

  describe('Edge Cases', () => {
    it('should handle rapid cleanup calls without errors', () => {
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      // Multiple cleanup calls should not cause errors
      expect(() => {
        cleanupDebouncer()
        cleanupDebouncer()
        cleanupDebouncer()
      }).not.toThrow()
    })

    it('should handle events with undefined/null properties gracefully', () => {
      expect(() => {
        logEventWithContext(
          'click_upgrade_button',
          'user123',
          undefined,
          undefined as any
        )
        logEventWithContext(
          'click_upgrade_button',
          'user123',
          undefined,
          null as any
        )
      }).not.toThrow()

      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1) // Should be debounced as same event
    })

    it('should handle events with complex nested properties', () => {
      const complexProperties = {
        context: 'test',
        current_tier: 'FREE',
        button_text: 'Upgrade',
        nested: {
          deep: {
            property: 'value',
            array: [1, 2, 3],
          },
        },
      }

      expect(() => {
        logEventWithContext(
          'click_upgrade_button',
          'user123',
          undefined,
          complexProperties
        )
      }).not.toThrow()

      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1)
    })

    it('should maintain separate debounce keys for different contexts when configured', () => {
      // This test assumes includeParametersInKey is false by default
      // Events with different contexts should still be debounced together
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'sidebar',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })
      logEventWithContext('click_upgrade_button', 'user123', undefined, {
        context: 'modal',
        current_tier: 'FREE',
        button_text: 'Upgrade',
      })

      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1) // Should be debounced as same event
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle many different events without performance degradation', () => {
      const startTime = Date.now()

      // Trigger many different events
      for (let i = 0; i < 100; i++) {
        logEventWithContext('click_upgrade_button', 'user123', undefined, {
          context: `test${i}`,
          current_tier: 'FREE',
          button_text: 'Upgrade',
        })
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      // Should complete quickly (less than 100ms for 100 events)
      expect(duration).toBeLessThan(100)

      // Should still only have one debounce entry (same event type)
      const stats = getDebounceStats()
      expect(stats.activeEntries).toBe(1)
    })

    it('should respect maxDebounceEntries limit', () => {
      const maxEntries = DEBOUNCE_CONFIG.maxDebounceEntries

      // This test would require creating more unique events than maxEntries
      // For now, just verify the configuration exists
      expect(maxEntries).toBeGreaterThan(0)
      expect(typeof maxEntries).toBe('number')
    })
  })
})
