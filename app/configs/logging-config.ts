/**
 * Centralized Logging Configuration
 *
 * This file contains all event names, descriptive labels, logging service flags,
 * event categorization and metadata for the logging system.
 *
 * This is the single source of truth for all logging-related settings.
 */

// ============================================================================
// LOGGING SERVICE FLAGS
// ============================================================================

export type LoggingFlags = {
  logPosthog: boolean
  logMemory: boolean // for future database logging
}

export const LOGGING_FLAGS: LoggingFlags = {
  logPosthog: true,
  logMemory: false, // Not implemented yet
}

// ============================================================================
// PROVIDER CONFIGURATION
// ============================================================================

/**
 * Provider Configuration
 *
 * To swap analytics providers, simply change the ACTIVE_PROVIDER setting
 * and update the corresponding service implementation in app/libs/logging.ts
 *
 * Supported providers:
 * - 'posthog': PostHog Analytics (default)
 * - 'amplitude': Amplitude Analytics
 * - 'mixpanel': Mixpanel Analytics
 * - 'custom': Custom analytics implementation
 */
export type AnalyticsProvider = 'posthog' | 'amplitude' | 'mixpanel' | 'custom'

export const ACTIVE_PROVIDER: AnalyticsProvider = 'posthog'

/**
 * Provider-specific configuration
 * Add new providers here as needed
 */
export const PROVIDER_CONFIG = {
  posthog: {
    name: 'PostHog',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_POSTHOG_KEY',
  },
  amplitude: {
    name: 'Amplitude',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_AMPLITUDE_API_KEY',
  },
  mixpanel: {
    name: 'Mixpanel',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN',
  },
  custom: {
    name: 'Custom Analytics',
    requiresApiKey: false,
    envVar: null,
  },
} as const

// ============================================================================
// EVENT CATEGORIES
// ============================================================================

export const EVENT_CATEGORIES = {
  SCREENING: 'screening',
  DRAGTREE: 'dragtree',
  AI_PANE: 'aipane',
  SETTINGS: 'settings',
  NAVIGATION: 'navigation',
  SUBSCRIPTION: 'subscription',
  FEEDBACK: 'feedback',
} as const

export type EventCategory =
  (typeof EVENT_CATEGORIES)[keyof typeof EVENT_CATEGORIES]

// ============================================================================
// EVENT DEFINITIONS
// ============================================================================

export type EventName =
  // Screening Module Events
  | 'click_screening_paraphraseAndAnalyze'
  | 'click_screening_startClarification'

  // DragTree Module Events
  | 'click_outline_quickResearch'
  | 'click_reactflow_quickResearch'
  | 'click_reactflow_hierarchical'
  | 'click_reactflow_circular'
  | 'edit_outline_title'
  | 'click_dragtree_batchResearch'
  | 'click_settings_copyOriginal'
  | 'click_settings_copyMarkdown'

  // AI Pane Module Events
  | 'click_aipane_startGenerate'
  | 'click_aipane_copyGenerated'
  | 'click_aipane_startChat'
  | 'click_aipane_sendMessage'

  // Subscription Module Events
  | 'click_upgrade_sidebar'
  | 'click_manage_subscription_profile'
  | 'click_subscribe_profile'
  | 'click_refresh_account_profile'
  | 'start_subscription_checkout'
  | 'error_subscription_checkout'
  | 'click_subscription_manage'

  // Unified Upgrade System Events
  | 'click_upgrade_button'
  | 'click_upgrade_hint'
  | 'click_upgrade_banner'
  | 'dismiss_upgrade_banner'

  // Feedback Module Events
  | 'click_feedback_widget_opened'
  | 'submit_feedback_widget'

// ============================================================================
// EVENT METADATA
// ============================================================================

export type EventMetadata = {
  name: EventName
  label: string
  description: string
  category: EventCategory
  requiresUserId: boolean
  requiresDragTreeId: boolean
  optionalProperties?: string[]
}

export const EVENT_DEFINITIONS: Record<EventName, EventMetadata> = {
  // Screening Module
  click_screening_paraphraseAndAnalyze: {
    name: 'click_screening_paraphraseAndAnalyze',
    label: 'Screening: Paraphrase & Analyze Click',
    description: 'User clicked on paragraphs in screening module',
    category: EVENT_CATEGORIES.SCREENING,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['description_length', 'selected_language'],
  },

  click_screening_startClarification: {
    name: 'click_screening_startClarification',
    label: 'Screening: Start Clarification Click',
    description: 'User clicked "start clarify" button',
    category: EVENT_CATEGORIES.SCREENING,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['description_length', 'selected_suggestion'],
  },

  // DragTree Module
  click_outline_quickResearch: {
    name: 'click_outline_quickResearch',
    label: 'Outline: Quick Research Click',
    description: 'User clicked quick research in outline view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'question_text'],
  },

  click_reactflow_quickResearch: {
    name: 'click_reactflow_quickResearch',
    label: 'React Flow: Quick Research Click',
    description: 'User clicked quick research in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'question_text'],
  },

  click_reactflow_hierarchical: {
    name: 'click_reactflow_hierarchical',
    label: 'React Flow: Hierarchical Plot Click',
    description: 'User clicked hierarchical plot in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  click_reactflow_circular: {
    name: 'click_reactflow_circular',
    label: 'React Flow: Circular Plot Click',
    description: 'User clicked circular plot in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  edit_outline_title: {
    name: 'edit_outline_title',
    label: 'Outline: Title Edit',
    description: 'User successfully edited dragTree node title',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'original_text', 'final_text'],
  },

  click_dragtree_batchResearch: {
    name: 'click_dragtree_batchResearch',
    label: 'DragTree: Batch Research Click',
    description: 'User batch selected questions and clicked "start research"',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_ids', 'question_texts', 'batch_count'],
  },

  click_settings_copyOriginal: {
    name: 'click_settings_copyOriginal',
    label: 'Settings: Copy Original Click',
    description: 'User copied original question in settings',
    category: EVENT_CATEGORIES.SETTINGS,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  click_settings_copyMarkdown: {
    name: 'click_settings_copyMarkdown',
    label: 'Settings: Copy Markdown Click',
    description: 'User copied markdown in settings',
    category: EVENT_CATEGORIES.SETTINGS,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  // AI Pane Module
  click_aipane_startGenerate: {
    name: 'click_aipane_startGenerate',
    label: 'AI Pane: Start Generate Click',
    description: 'User clicked "start generate" in AI pane generate tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['prompt_length', 'context_count'],
  },

  click_aipane_copyGenerated: {
    name: 'click_aipane_copyGenerated',
    label: 'AI Pane: Copy Generated Click',
    description: 'User clicked copy button in AI pane generate tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['content_length', 'generation_state', 'asset_id'],
  },

  click_aipane_startChat: {
    name: 'click_aipane_startChat',
    label: 'AI Pane: Start Chat Click',
    description: 'User clicked "start chat" in AI pane chat tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['initial_message_length', 'context_count'],
  },

  click_aipane_sendMessage: {
    name: 'click_aipane_sendMessage',
    label: 'AI Pane: Send Message Click',
    description: 'User sent a message in AI pane chat',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['message_text', 'message_length'],
  },

  // Subscription Module
  click_upgrade_sidebar: {
    name: 'click_upgrade_sidebar',
    label: 'Subscription: Upgrade Click (Sidebar)',
    description: 'User clicked upgrade button in sidebar',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['source', 'current_tier'],
  },

  click_manage_subscription_profile: {
    name: 'click_manage_subscription_profile',
    label: 'Subscription: Manage Subscription Click (Profile)',
    description: 'User clicked manage subscription in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier', 'customer_id'],
  },

  click_subscribe_profile: {
    name: 'click_subscribe_profile',
    label: 'Subscription: Subscribe Click (Profile)',
    description: 'User clicked subscribe button in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  click_refresh_account_profile: {
    name: 'click_refresh_account_profile',
    label: 'Subscription: Refresh Account Profile Click',
    description: 'User clicked refresh account button in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  start_subscription_checkout: {
    name: 'start_subscription_checkout',
    label: 'Subscription: Checkout Started',
    description: 'User started subscription checkout process',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['plan', 'billing_cycle'],
  },

  error_subscription_checkout: {
    name: 'error_subscription_checkout',
    label: 'Subscription: Checkout Error',
    description: 'Error occurred during subscription checkout',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['plan', 'billing_cycle', 'error'],
  },

  click_subscription_manage: {
    name: 'click_subscription_manage',
    label: 'Subscription: Manage Subscription Click',
    description: 'User clicked manage subscription button',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  // Unified Upgrade System Events
  click_upgrade_button: {
    name: 'click_upgrade_button',
    label: 'Upgrade: Button Click',
    description: 'User clicked unified upgrade button component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'button_text'],
  },

  click_upgrade_hint: {
    name: 'click_upgrade_hint',
    label: 'Upgrade: Hint Click',
    description: 'User clicked upgrade hint component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'message'],
  },

  click_upgrade_banner: {
    name: 'click_upgrade_banner',
    label: 'Upgrade: Banner Click',
    description: 'User clicked upgrade banner component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'title', 'message'],
  },

  dismiss_upgrade_banner: {
    name: 'dismiss_upgrade_banner',
    label: 'Upgrade: Banner Dismissed',
    description: 'User dismissed upgrade banner component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'title'],
  },

  // Feedback Module
  click_feedback_widget_opened: {
    name: 'click_feedback_widget_opened',
    label: 'Feedback: Widget Opened',
    description: 'User opened the feedback widget modal',
    category: EVENT_CATEGORIES.FEEDBACK,
    requiresUserId: false,
    requiresDragTreeId: true,
    optionalProperties: [],
  },
  submit_feedback_widget: {
    name: 'submit_feedback_widget',
    label: 'Feedback: Widget Submitted',
    description: 'User submitted feedback through the feedback widget',
    category: EVENT_CATEGORIES.FEEDBACK,
    requiresUserId: false,
    requiresDragTreeId: true,
    optionalProperties: [
      'feedbackType',
      'engagementLevel',
      'hasRatings',
      'feedbackLength',
      'feedbackRecordId',
    ],
  },
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export function isValidEventName(eventName: string): eventName is EventName {
  return eventName in EVENT_DEFINITIONS
}

export function getEventMetadata(eventName: EventName): EventMetadata {
  return EVENT_DEFINITIONS[eventName]
}

export function getEventsByCategory(category: EventCategory): EventMetadata[] {
  return Object.values(EVENT_DEFINITIONS).filter(
    event => event.category === category
  )
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Types are already exported above, no need to re-export
