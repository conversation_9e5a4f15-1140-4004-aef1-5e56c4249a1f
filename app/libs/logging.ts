/**
 * Centralized Logging Utility System
 *
 * This utility provides a unified interface for logging events across different services.
 * It supports PostHog analytics integration and has an extensible architecture for future services.
 *
 * Features:
 * - PostHog integration as primary service
 * - Extensible architecture for future services (memory logging to database)
 * - Feature flags to control which logging services are active
 * - Non-blocking operation to prevent impact on user experience
 * - Proper error handling and fallbacks
 */

import {
  trackEvent as posthogTrackEvent,
  identifyUser as posthogIdentifyUser,
} from '@/app/libs/posthog'
import {
  EventName,
  LOGGING_FLAGS,
  isValidEventName,
  getEventMetadata,
  DEBOUNCE_CONFIG,
  EVENT_DEBOUNCE_INTERVALS,
} from '@/app/configs/logging-config'

// Re-export types for external use
export type { EventName } from '@/app/configs/logging-config'
import { validateEventProperties } from '@/app/libs/logging-validation'

// ============================================================================
// TYPESCRIPT-ENFORCED EVENT PROPERTIES
// ============================================================================

// Define exact property types for each event
export type EventPropertyTypes = {
  // Screening Module Events
  click_screening_paraphraseAndAnalyze: {
    description_length: number
    selected_language: string
    use_simulator: boolean
  }
  click_screening_startClarification: {
    description_length: number
    selected_suggestion: string
    selected_language: string
  }

  // DragTree Module Events
  click_outline_quickResearch: {
    question_id: string
    question_text: string
    research_option: string
    research_label: string
  }
  click_reactflow_quickResearch: {
    question_id: string
    question_text: string
    research_option: string
    research_label: string
  }
  click_reactflow_hierarchical: {
    previous_mode: string
    new_mode: string
  }
  click_reactflow_circular: {
    previous_mode: string
    new_mode: string
  }
  edit_outline_title: {
    old_title: string
    new_title: string
    title_length: number
  }
  click_dragtree_batchResearch: {
    question_ids: string[]
    question_texts: string[]
    batch_count: number
    tier: string
    effective_limit: number
  }
  click_settings_copyOriginal: {
    content_length: number
    copy_format: string
  }
  click_settings_copyMarkdown: {
    content_length: number
    copy_format: string
  }

  // AI Pane Module Events
  click_aipane_startGenerate: {
    prompt_length: number
    context_count: number
    total_tokens: number
    settings_type: string
  }
  click_aipane_copyGenerated: {
    content_length: number
    generation_state: string
    asset_id: string
  }
  click_aipane_startChat: {
    context_count: number
    initial_message_length: number
    total_tokens: number
    settings_type: string
  }
  click_aipane_sendMessage: {
    message_length: number
    conversation_id: string
    context_count: number
  }

  // Subscription Module Events
  click_upgrade_sidebar: {
    source: string
    current_tier: string
  }
  click_manage_subscription_profile: {
    current_tier: string | undefined
    customer_id: string | undefined
  }
  click_subscribe_profile: {
    current_tier: string | undefined
  }
  click_refresh_account_profile: {
    current_tier: string | undefined
  }
  start_subscription_checkout: {
    plan: string
    billing_cycle: string
  }
  error_subscription_checkout: {
    plan: string
    billing_cycle: string
    error: string
  }
  click_subscription_manage: {
    current_tier: string
  }

  // Unified Upgrade System Events
  click_upgrade_button: {
    context: string
    current_tier: string
    button_text: string
  }
  click_upgrade_hint: {
    context: string
    current_tier: string
    message: string
  }
  click_upgrade_banner: {
    context: string
    current_tier: string
    title: string
    message: string
  }
  dismiss_upgrade_banner: {
    context: string
    current_tier: string
    title: string
  }

  // Feedback Module Events
  click_feedback_widget_opened: {
    // No additional properties required
  }
  submit_feedback_widget: {
    feedbackType: string | null
    engagementLevel: string | null
    hasRatings: boolean
    feedbackLength: number
    feedbackRecordId: string | undefined
  }
}

// ============================================================================
// TYPES
// ============================================================================

export type LoggingContext = {
  userId?: string
  dragTreeId?: string
  sessionId?: string
  timestamp?: string
  userAgent?: string
  url?: string
}

export type EventProperties = Record<string, any>

export type LoggingService = 'posthog' | 'memory'

// ============================================================================
// MEMORY LOGGING INTERFACE (STUBBED)
// ============================================================================

/**
 * Memory logging service - stores events for future database persistence
 * Currently stubbed out - will be implemented when database logging is needed
 */
class MemoryLoggingService {
  private events: Array<{
    eventName: EventName
    properties: EventProperties
    context: LoggingContext
    timestamp: Date
  }> = []

  async logEvent(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext
  ): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[MemoryLogging] ${eventName}:`, { properties, context })
    }

    // // Store event in memory for future database persistence
    // this.events.push({
    //   eventName,
    //   properties,
    //   context,
    //   timestamp: new Date(),
    // })

    // TODO: Implement database persistence
    // This could batch events and periodically flush to database
  }

  async identifyUser(
    userId: string,
    properties?: EventProperties
  ): Promise<void> {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[MemoryLogging] Identify user: ${userId}`, properties)
    }
    // TODO: Implement user identification storage
  }

  getStoredEvents() {
    return [...this.events]
  }

  clearStoredEvents() {
    this.events = []
  }
}

// ============================================================================
// POSTHOG LOGGING SERVICE
// ============================================================================

/**
 * PostHog logging service - integrates with existing PostHog setup
 */
class PostHogLoggingService {
  async logEvent(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext
  ): Promise<void> {
    try {
      const enrichedProperties = {
        ...properties,
        ...context,
        event_category: getEventMetadata(eventName).category,
        timestamp: new Date().toISOString(),
      }

      posthogTrackEvent(eventName, enrichedProperties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error(
          `[PostHogLogging] Failed to log event ${eventName}:`,
          error
        )
      }
    }
  }

  async identifyUser(
    userId: string,
    properties?: EventProperties
  ): Promise<void> {
    try {
      posthogIdentifyUser(userId, properties)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('[PostHogLogging] Failed to identify user:', error)
      }
    }
  }
}

// ============================================================================
// TYPE-SAFE LOGGING FUNCTIONS
// ============================================================================

/**
 * Type-safe logging function with full TypeScript validation
 * Enforces correct properties at compile time for each event
 */
export function logEventWithContext<T extends EventName>(
  eventName: T,
  userId: string | undefined,
  dragTreeId: string | undefined,
  properties: EventPropertyTypes[T]
): void {
  // Call the main logging utility with validated properties
  loggingUtility.logEvent(eventName, properties as EventProperties, {
    userId,
    dragTreeId,
  })
}

// ============================================================================
// DEBOUNCING UTILITIES
// ============================================================================

/**
 * Debounce entry for tracking pending log events
 */
type DebounceEntry = {
  timeoutId: NodeJS.Timeout
  timestamp: number
  eventName: EventName
  properties: EventProperties
  context: LoggingContext
}

/**
 * Event debouncing manager for preventing duplicate log events
 * Handles memory cleanup and edge cases like component unmounting
 */
class EventDebouncer {
  private debounceMap = new Map<string, DebounceEntry>()
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor() {
    // Start periodic cleanup if debouncing is enabled
    if (DEBOUNCE_CONFIG.enabled) {
      this.startPeriodicCleanup()
    }

    // Browser-specific cleanup on page unload
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => this.cleanup())
      window.addEventListener('pagehide', () => this.cleanup())
    }
  }

  /**
   * Generate debounce key for an event
   * Can include parameters for fine-grained debouncing control
   */
  private generateDebounceKey(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext
  ): string {
    let key = eventName

    if (DEBOUNCE_CONFIG.includeParametersInKey) {
      // Include context for user/dragTree specific debouncing
      if (context.userId) key += `:user:${context.userId}`
      if (context.dragTreeId) key += `:tree:${context.dragTreeId}`

      // Include critical properties that should affect debouncing
      // For example, different research options should be tracked separately
      if (properties.research_option)
        key += `:option:${properties.research_option}`
      if (properties.settings_type) key += `:type:${properties.settings_type}`
    }

    return key
  }

  /**
   * Get debounce interval for a specific event
   */
  private getDebounceInterval(eventName: EventName): number {
    return (
      EVENT_DEBOUNCE_INTERVALS[eventName] ?? DEBOUNCE_CONFIG.defaultIntervalMs
    )
  }

  /**
   * Check if event should be debounced and handle the debouncing logic
   * Returns true if the event should be logged immediately, false if debounced
   */
  shouldLogEvent(
    eventName: EventName,
    properties: EventProperties,
    context: LoggingContext,
    logFunction: () => Promise<void>
  ): boolean {
    if (!DEBOUNCE_CONFIG.enabled) {
      return true // No debouncing, log immediately
    }

    const debounceKey = this.generateDebounceKey(eventName, properties, context)
    const existingEntry = this.debounceMap.get(debounceKey)
    const debounceInterval = this.getDebounceInterval(eventName)

    // If there's an existing entry, clear it and create a new one
    if (existingEntry) {
      clearTimeout(existingEntry.timeoutId)
    }

    // Create new debounce entry
    const timeoutId = setTimeout(async () => {
      try {
        await logFunction()
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error(
            `[EventDebouncer] Failed to log debounced event ${eventName}:`,
            error
          )
        }
      } finally {
        // Clean up the debounce entry
        this.debounceMap.delete(debounceKey)
      }
    }, debounceInterval)

    // Store the debounce entry
    const entry: DebounceEntry = {
      timeoutId,
      timestamp: Date.now(),
      eventName,
      properties,
      context,
    }

    this.debounceMap.set(debounceKey, entry)

    // Prevent memory leaks by enforcing max entries
    if (this.debounceMap.size > DEBOUNCE_CONFIG.maxDebounceEntries) {
      this.cleanupOldestEntries()
    }

    return false // Event is debounced, don't log immediately
  }

  /**
   * Clean up oldest debounce entries to prevent memory leaks
   */
  private cleanupOldestEntries(): void {
    const entries = Array.from(this.debounceMap.entries())
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

    // Remove oldest 10% of entries
    const toRemove = Math.ceil(entries.length * 0.1)
    for (let i = 0; i < toRemove; i++) {
      const [key, entry] = entries[i]
      clearTimeout(entry.timeoutId)
      this.debounceMap.delete(key)
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(
        `[EventDebouncer] Cleaned up ${toRemove} old debounce entries`
      )
    }
  }

  /**
   * Start periodic cleanup of expired entries
   */
  private startPeriodicCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now()
      const expiredKeys: string[] = []

      this.debounceMap.forEach((entry, key) => {
        if (
          now - entry.timestamp >
          this.getDebounceInterval(entry.eventName) + 5000
        ) {
          // Entry is expired (5 second buffer)
          expiredKeys.push(key)
        }
      })

      expiredKeys.forEach(key => {
        const entry = this.debounceMap.get(key)
        if (entry) {
          clearTimeout(entry.timeoutId)
          this.debounceMap.delete(key)
        }
      })

      if (expiredKeys.length > 0 && process.env.NODE_ENV === 'development') {
        console.log(
          `[EventDebouncer] Cleaned up ${expiredKeys.length} expired debounce entries`
        )
      }
    }, DEBOUNCE_CONFIG.cleanupIntervalMs)
  }

  /**
   * Clean up all debounce entries and timers
   */
  cleanup(): void {
    // Clear all pending timeouts
    this.debounceMap.forEach(entry => {
      clearTimeout(entry.timeoutId)
    })
    this.debounceMap.clear()

    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }

  /**
   * Get current debounce statistics (for debugging/monitoring)
   */
  getStats() {
    return {
      activeEntries: this.debounceMap.size,
      maxEntries: DEBOUNCE_CONFIG.maxDebounceEntries,
      enabled: DEBOUNCE_CONFIG.enabled,
    }
  }
}

// ============================================================================
// MAIN LOGGING UTILITY
// ============================================================================

class LoggingUtility {
  private posthogService = new PostHogLoggingService()
  private memoryService = new MemoryLoggingService()
  private debouncer = new EventDebouncer()

  /**
   * Log an event across all enabled services with debouncing support
   */
  async logEvent(
    eventName: EventName,
    properties: EventProperties = {},
    context: LoggingContext = {}
  ): Promise<void> {
    // Validate event name
    if (!isValidEventName(eventName)) {
      if (process.env.NODE_ENV === 'development') {
        console.error(`[Logging] Invalid event name: ${eventName}`)
      }
      return
    }

    // Comprehensive validation with warnings for missing optional properties
    const validation = validateEventProperties(eventName, properties, context)

    // Log validation results in development
    if (process.env.NODE_ENV === 'development') {
      validation.errors.forEach(error => console.error(`[Logging] ${error}`))
      validation.warnings.forEach(warning =>
        console.warn(`[Logging] ${warning}`)
      )
    }

    // Continue with logging even if validation has warnings (non-blocking)
    const metadata = getEventMetadata(eventName)

    // Add metadata to properties
    const enrichedProperties = {
      ...properties,
      event_label: metadata.label,
      event_description: metadata.description,
    }

    // Define the actual logging function
    const executeLogging = async (): Promise<void> => {
      const promises: Promise<void>[] = []

      if (LOGGING_FLAGS.logPosthog) {
        promises.push(
          this.posthogService.logEvent(eventName, enrichedProperties, context)
        )
      }

      if (LOGGING_FLAGS.logMemory) {
        promises.push(
          this.memoryService.logEvent(eventName, enrichedProperties, context)
        )
      }

      // Execute all logging operations without blocking
      Promise.allSettled(promises).catch(error => {
        if (process.env.NODE_ENV === 'development') {
          console.error('[Logging] Some logging services failed:', error)
        }
      })
    }

    // Check if event should be debounced
    const shouldLogImmediately = this.debouncer.shouldLogEvent(
      eventName,
      properties,
      context,
      executeLogging
    )

    // If not debounced, log immediately
    if (shouldLogImmediately) {
      await executeLogging()
    }
    // If debounced, the debouncer will handle the logging after the delay
  }

  /**
   * Identify a user across all enabled services
   */
  async identifyUser(
    userId: string,
    properties: EventProperties = {}
  ): Promise<void> {
    const promises: Promise<void>[] = []

    if (LOGGING_FLAGS.logPosthog) {
      promises.push(this.posthogService.identifyUser(userId, properties))
    }

    if (LOGGING_FLAGS.logMemory) {
      promises.push(this.memoryService.identifyUser(userId, properties))
    }

    // Execute all identification operations without blocking
    Promise.allSettled(promises).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.error('[Logging] Some user identification failed:', error)
      }
    })
  }

  /**
   * Get stored events from memory service (for debugging/testing)
   */
  getStoredEvents() {
    return this.memoryService.getStoredEvents()
  }

  /**
   * Clear stored events from memory service
   */
  clearStoredEvents() {
    this.memoryService.clearStoredEvents()
  }

  /**
   * Get debouncing statistics (for debugging/monitoring)
   */
  getDebounceStats() {
    return this.debouncer.getStats()
  }

  /**
   * Clean up debouncing resources (for testing/cleanup)
   */
  cleanupDebouncer() {
    this.debouncer.cleanup()
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

const loggingUtility = new LoggingUtility()

// ============================================================================
// EXPORTED FUNCTIONS
// ============================================================================

/**
 * Log an event with the centralized logging system
 */
export const logEvent = (
  eventName: EventName,
  properties?: EventProperties,
  context?: LoggingContext
) => {
  loggingUtility.logEvent(eventName, properties, context)
}

/**
 * Identify a user with the centralized logging system
 */
export const identifyUser = (userId: string, properties?: EventProperties) => {
  loggingUtility.identifyUser(userId, properties)
}

/**
 * Get stored events (for debugging/testing)
 */
export const getStoredEvents = () => {
  return loggingUtility.getStoredEvents()
}

/**
 * Clear stored events
 */
export const clearStoredEvents = () => {
  loggingUtility.clearStoredEvents()
}

/**
 * Get debouncing statistics (for debugging/monitoring)
 */
export const getDebounceStats = () => {
  return loggingUtility.getDebounceStats()
}

/**
 * Clean up debouncing resources (for testing/cleanup)
 */
export const cleanupDebouncer = () => {
  loggingUtility.cleanupDebouncer()
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Create a logging context from common sources
 */
export const createLoggingContext = (
  userId?: string,
  dragTreeId?: string,
  additionalContext?: Partial<LoggingContext>
): LoggingContext => {
  return {
    userId,
    dragTreeId,
    sessionId:
      typeof window !== 'undefined'
        ? window.sessionStorage?.getItem('sessionId') || undefined
        : undefined,
    timestamp: new Date().toISOString(),
    userAgent:
      typeof window !== 'undefined' ? window.navigator?.userAgent : undefined,
    url: typeof window !== 'undefined' ? window.location?.href : undefined,
    ...additionalContext,
  }
}

// Types are already exported above, no need to re-export
